package life.ljs.codeagent.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;

/**
 * <AUTHOR> carp
 * @version 1.0.0
 * @description
 * @since 2025/8/21
 **/
@Data
@Component
@ConfigurationProperties(prefix = "agent")
public class AgentConfig {

    private LLMConfig llm = new LLMConfig();
    private ToolsConfig tools = new ToolsConfig();

    @Data
    public static class LLMConfig {
        private String provider = "qwen";
        private String apiKey;
        private String baseUrl;
        private String model = "qwen-plus";
        private Duration timeout = Duration.ofSeconds(30);
        private int maxTokens = 32768;
        private double temperature = 0.1;
    }
    @Data
    public static class ToolsConfig {
        private FileSearchConfig fileSearch = new FileSearchConfig();
        private CodeAnalysisConfig codeAnalysis = new CodeAnalysisConfig();
        private CacheConfig cache = new CacheConfig();

        @Data
        public static class FileSearchConfig {
            private int maxResults = 20;
            private List<String> supportedExtensions = List.of(".java", ".xml", ".properties", ".yml", ".md");
            private List<String> excludeDirs = List.of("target", ".git", ".idea", "node_modules");
        }

        @Data
        public static class CodeAnalysisConfig {
            private int maxComplexity = 10;
            private int maxLineLength = 120;
        }

        @Data
        public static class CacheConfig {
            private boolean enabled = true;
            private int maxSize = 1000;
            private Duration expireAfterWrite = Duration.ofMinutes(30);
        }
    }
}
