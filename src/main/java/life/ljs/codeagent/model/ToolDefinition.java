package life.ljs.codeagent.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR> carp
 * @version 1.0.0
 * @description
 * @since 2025/8/21
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ToolDefinition {
    private String type = "function";
    private FunctionDefinition functionDefinition;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    private static class FunctionDefinition {
        private String name;
        private String description;
        private Map<String,Object> parameters;
    }
}

