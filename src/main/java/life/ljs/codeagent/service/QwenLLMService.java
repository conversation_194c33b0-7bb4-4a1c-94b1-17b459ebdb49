package life.ljs.codeagent.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import life.ljs.codeagent.config.AgentConfig;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR> carp
 * @version 1.0.0
 * @description
 * @since 2025/8/21
 **/
@Slf4j
@Service
public class QwenLLMService {
    private final AgentConfig agentConfig;
    private final ObjectMapper objectMapper;
    private final OkHttpClient okHttpClient;

    public QwenLLMService(AgentConfig agentConfig, ObjectMapper objectMapper){
        this.agentConfig = agentConfig;
        this.objectMapper = objectMapper;
        this.okHttpClient = new OkHttpClient();
    }
}
